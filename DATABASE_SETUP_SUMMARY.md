# Kim Rebill Database Setup Summary

## 📋 Migration & Seeder Files Created

### Migration Files (dalam urutan eksekusi):
1. **2014_10_11_000000_create_organizations_table.php** - Tabel organizations
2. **2014_10_12_000000_create_users_table.php** - Tabel users dengan relasi ke organizations
3. **2014_10_12_100000_create_password_resets_table.php** - Tabel password resets
4. **2019_08_19_000000_create_failed_jobs_table.php** - Tabel failed jobs
5. **2019_12_14_000001_create_personal_access_tokens_table.php** - Tabel personal access tokens
6. **2024_01_01_000000_create_subscriptions_table.php** - Tabel subscriptions
7. **2024_01_02_000000_create_invoices_table.php** - Tabel invoices
8. **2024_01_03_000000_create_recurring_templates_table.php** - Tabel recurring templates
9. **2024_01_04_000000_create_email_templates_table.php** - Tabel email templates

### Seeder Files:
1. **AdminSeeder.php** - Membuat admin organization, subscription, dan super admin user
2. **EmailTemplateSeeder.php** - Membuat default email templates untuk recurring invoices

### Model Files Created/Updated:
1. **RecurringTemplate.php** - Model untuk recurring templates
2. **EmailTemplate.php** - Model untuk email templates

## 🗄️ Database Schema

### Organizations Table
- Multi-tenant structure dengan Bexio integration
- Subscription management (trial/active/inactive)
- Company profile dan billing information

### Users Table  
- Relasi ke organizations
- Bexio OAuth integration (access_token, refresh_token)
- Role-based access (user/admin/super_admin)
- Admin dan subscription status management

### Invoices Table
- Support untuk recurring dan non-recurring invoices
- Bexio integration dengan sync tracking
- JSON fields untuk contact_info, items, recurring_settings
- Status tracking dan processing timestamps

### Recurring Templates Table
- Template untuk recurring invoices
- Flexible interval system (daily/weekly/monthly/yearly)
- JSON positions untuk invoice items
- Execution tracking (last_executed, next_run)

### Email Templates Table
- Default dan custom email templates
- Template variables support
- Organization-specific customization

### Subscriptions Table
- Billing dan subscription management
- Trial dan paid subscription tracking
- Payment method dan billing details

## 🚀 Seeder Data

### Admin User (Super Admin):
- **Email**: <EMAIL>
- **Password**: admin@2025
- **Role**: super_admin
- **Organization**: Kim Rebill Admin

### Email Templates (4 default templates):
1. **Recurring Invoice Email** - Template untuk recurring invoice emails
2. **Invoice Reminder** - Template untuk payment reminders
3. **Welcome Email** - Template untuk welcome emails
4. **Payment Confirmation** - Template untuk payment confirmations

## 🛠️ Commands & Scripts

### Artisan Commands:
- **`php artisan rebill:process-recurring`** - Process recurring invoices
  - `--dry-run` - Show what would be processed
  - `--user=ID` - Process only for specific user
  - `--force` - Force process even if not due

### Reset Script:
- **`reset_database.sh`** - Reset database dengan fresh migrations dan seeders

## 📝 Usage Instructions

### 1. Reset Database (HANYA jika diminta):
```bash
# Jangan jalankan kecuali diminta user
./reset_database.sh
```

### 2. Manual Migration & Seeding:
```bash
cd laravel-app

# Drop semua tables
php artisan db:wipe --force

# Run fresh migrations
php artisan migrate:fresh --force

# Run seeders
php artisan db:seed --force

# Check status
php artisan migrate:status
```

### 3. Testing Recurring Invoices:
```bash
# Dry run untuk melihat apa yang akan diproses
php artisan rebill:process-recurring --dry-run

# Process recurring invoices
php artisan rebill:process-recurring

# Process untuk user tertentu
php artisan rebill:process-recurring --user=1
```

## 🔐 Login Credentials

### Super Admin:
- **URL**: `/admin/login` atau `/login`
- **Email**: <EMAIL>  
- **Password**: admin@2025

### Regular Users:
- Gunakan Bexio OAuth login
- Organization dan user akan dibuat otomatis saat first login

## 📊 Data Flow

1. **User Registration**: Via Bexio OAuth → Creates organization & user
2. **Invoice Creation**: Manual atau via recurring templates
3. **Recurring Processing**: Artisan command → Creates invoices → Sends to Bexio
4. **Email Notifications**: Using customizable email templates
5. **Admin Management**: Super admin dapat manage users dan organizations

## 🎯 Key Features

- ✅ Multi-tenant architecture
- ✅ Bexio OAuth integration  
- ✅ Recurring invoice automation
- ✅ Customizable email templates
- ✅ Admin management system
- ✅ Subscription management
- ✅ Comprehensive logging
- ✅ Dry-run testing capabilities

## 📋 Next Steps

1. Test Bexio OAuth login untuk create organizations/users
2. Test recurring invoice creation dan processing
3. Test email template customization
4. Test admin management features
5. Setup cron job untuk automated recurring processing
