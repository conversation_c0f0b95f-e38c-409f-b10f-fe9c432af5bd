#!/bin/bash

# Script untuk reset database dan migration Kim <PERSON> App
# Jangan jalan<PERSON> script ini kecuali diminta oleh user

echo "=== Kim <PERSON>bill Database Reset Script ==="
echo "PERINGATAN: Script ini akan menghapus semua data database!"
echo "Pastikan Anda sudah backup data penting sebelum melanjutkan."
echo ""

# Pindah ke direktori <PERSON>
cd laravel-app

echo "1. Dropping all tables..."
php artisan db:wipe --force

echo "2. Running fresh migrations..."
php artisan migrate:fresh --force

echo "3. Running seeders..."
php artisan db:seed --force

echo "4. Checking migration status..."
php artisan migrate:status

echo ""
echo "=== Database Reset Complete ==="
echo "Database telah direset dengan schema dan data baru."
echo ""
echo "Login credentials:"
echo "- Super Admin: <EMAIL> / admin@2025"
echo "- Demo User: <EMAIL> (Bexio OAuth)"
echo "- Active User: <EMAIL> (Bexio OAuth)"
echo ""
echo "Organizations created:"
echo "- Demo Company AG (trial)"
echo "- Active Business GmbH (active)"
echo "- <PERSON> (admin org)"
echo ""
echo "Sample data includes:"
echo "- 6 users (including admin)"
echo "- 3 organizations with subscriptions"
echo "- 10+ invoices (recurring templates + generated invoices)"
echo "- 6+ recurring templates"
