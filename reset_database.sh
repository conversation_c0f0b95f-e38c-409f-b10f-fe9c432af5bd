#!/bin/bash

# <PERSON>ript untuk reset database dan migration Kim <PERSON> App
# Jangan jalankan script ini kecuali diminta oleh user

echo "=== Kim <PERSON> Database Reset Script ==="
echo "PERINGATAN: Script ini akan menghapus semua data database!"
echo "Pastikan Anda sudah backup data penting sebelum melanjutkan."
echo ""

# Pindah ke direktori Laravel
cd laravel-app

echo "1. Dropping all tables..."
php artisan db:wipe --force

echo "2. Running fresh migrations..."
php artisan migrate:fresh --force

echo "3. Running seeders..."
php artisan db:seed --force

echo "4. Checking migration status..."
php artisan migrate:status

echo ""
echo "=== Database Reset Complete ==="
echo "Database telah direset dengan schema dan data minimal."
echo ""
echo "Login credentials:"
echo "- Super Admin: <EMAIL> / admin@2025"
echo ""
echo "Data yang dibuat:"
echo "- 1 admin organization (<PERSON> Admin)"
echo "- 1 super admin user"
echo "- 4 default email templates untuk recurring invoices"
echo ""
echo "Untuk data lainnya, gunakan Bexio OAuth login untuk membuat"
echo "organization dan user baru secara otomatis."
