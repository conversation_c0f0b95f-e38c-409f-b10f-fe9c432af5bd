@extends('layouts.app')

@section('title', 'All Invoices')

@section('content')
<div class="container">
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Header -->
    <div class="row align-items-center justify-content-between mb-4">
        <div class="col-auto">
            <div class="mb-2">
                <h6 class="text-muted mb-1 fw-bold" style="font-size: 20px;">Recurring</h6>
                <h1 class="fw-bold mb-0" style="font-size: 40px; line-height: 1;">Invoices</h1>
            </div>
        </div>
        <div class="col-auto">
            <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create New
            </a>
        </div>
    </div>

    <!-- Metrics Cards -->
    <div class="row g-3 mb-4">
        @php
            // Since $invoices is already filtered to active only, no need to filter again
            $totalYRR = $invoices->where('is_recurring', true)->sum('total') * 12;
            $totalMRR = $invoices->where('is_recurring', true)->sum('total');
            $uniqueCustomers = $invoices->pluck('contact_info.name')->unique()->count();
            $recurringCount = $invoices->where('is_recurring', true)->count();
        @endphp

        <div class="col-md-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body p-3 text-center">
                    <h6 class="text-white-50 small mb-1">Total YRR</h6>
                    <h4 class="fw-bold mb-0 text-white">CHF {{ number_format($totalYRR, 0, '.', ',') }}</h4>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body p-3 text-center">
                    <h6 class="text-white-50 small mb-1">Total MRR</h6>
                    <h4 class="fw-bold mb-0 text-white">CHF {{ number_format($totalMRR, 0, '.', ',') }}</h4>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body p-3 text-center">
                    <h6 class="text-white-50 small mb-1">Customers</h6>
                    <h4 class="fw-bold mb-0 text-white">{{ $uniqueCustomers }}</h4>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body p-3 text-center">
                    <h6 class="text-white-50 small mb-1">Recurring Invoices</h6>
                    <h4 class="fw-bold mb-0 text-white">{{ $recurringCount }}</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Breakdown -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-light">
                <div class="card-body p-4">
                    @php
                        // Group invoices by customer
                        $customerGroups = $invoices->groupBy('contact_info.name');
                    @endphp

                    @if($customerGroups->count() > 0)
                        <div class="customer-breakdown">
                            @foreach($customerGroups as $customerName => $customerInvoices)
                                @php
                                    // Since $customerInvoices is already filtered to active only, no need to filter again
                                    $customerYRR = $customerInvoices->where('is_recurring', true)->sum('total') * 12;
                                    $firstInvoice = $customerInvoices->first();
                                @endphp

                                <div class="card mb-3 border-0 shadow-sm">
                                    <!-- Customer Header -->
                                    <div class="card-header bg-white border-bottom-0 py-3">
                                        <div class="row align-items-center">
                                            <div class="col">
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 45px; height: 45px;">
                                                        <span class="fw-bold">{{ substr($customerName ?? 'U', 0, 1) }}</span>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1 fw-semibold">{{ $customerName ?? 'Unknown Customer' }}</h6>
                                                        <small class="text-muted">{{ $firstInvoice->contact_info['email'] ?? '' }}</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <div class="d-flex align-items-center">
                                                    <span class="fw-semibold me-3">YRR: CHF {{ number_format($customerYRR, 0, '.', ',') }}</span>
                                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#customer-{{ $loop->index }}" aria-expanded="true">
                                                        <i class="fas fa-chevron-down"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Customer Invoices -->
                                    <div class="collapse show" id="customer-{{ $loop->index }}">
                                        <div class="card-body p-0">
                                            @foreach($customerInvoices as $invoice)
                                                <div class="d-flex align-items-center p-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                                                    <div class="me-3">
                                                        @if($invoice->is_recurring)
                                                            <div class="bg-info text-white rounded d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                                <i class="fas fa-sync-alt"></i>
                                                            </div>
                                                        @else
                                                            <div class="bg-secondary text-white rounded d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                                <i class="fas fa-file-invoice"></i>
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div class="row">
                                                            <div class="col-2">
                                                                <small class="text-muted fw-medium">Status</small>
                                                                @php
                                                                    $statusColors = [
                                                                        'draft' => 'warning',
                                                                        'active' => 'success'
                                                                    ];
                                                                    $statusText = $invoice->status === 'active' ? 'Active' : 'Draft';
                                                                @endphp
                                                                <div>
                                                                    <span class="badge bg-{{ $statusColors[$invoice->status] ?? 'secondary' }} bg-opacity-10 text-{{ $statusColors[$invoice->status] ?? 'secondary' }}">
                                                                        {{ $statusText }}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div class="col-2">
                                                                <small class="text-muted fw-medium">Next Charge</small>
                                                                <div class="fw-medium">
                                                                    @if($invoice->recurringTemplate && $invoice->recurringTemplate->next_run)
                                                                        {{ \Carbon\Carbon::parse($invoice->recurringTemplate->next_run)->format('M j, Y') }}
                                                                    @else
                                                                        {{ $invoice->created_at->format('M j, Y') }}
                                                                    @endif
                                                                </div>
                                                            </div>
                                                            <div class="col-3">
                                                                <small class="text-muted fw-medium">Description</small>
                                                                <div class="fw-medium">{{ $invoice->title ?? $invoice->recurring_settings['rebill_description'] ?? $invoice->document_nr }}</div>
                                                            </div>
                                                            <div class="col-2">
                                                                <small class="text-muted fw-medium">Total Price</small>
                                                                <div class="fw-medium">CHF {{ number_format($invoice->total, 0, '.', ',') }}</div>
                                                            </div>
                                                            <div class="col-2">
                                                                <small class="text-muted fw-medium">Billing Period</small>
                                                                <div class="fw-medium text-capitalize">
                                                                    @php
                                                                        $billingPeriod = 'Monthly'; // Default
                                                                        if ($invoice->recurringTemplate) {
                                                                            $billingPeriod = ucfirst($invoice->recurringTemplate->interval);
                                                                        } elseif (isset($invoice->recurring_settings['interval'])) {
                                                                            $billingPeriod = ucfirst($invoice->recurring_settings['interval']);
                                                                        }
                                                                    @endphp
                                                                    {{ $billingPeriod }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="ms-3 d-flex justify-content-end">
                                                        <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-sm btn-outline-secondary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No invoices found</h5>
                            <p class="text-muted">Create your first invoice to get started.</p>
                            <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Create New Invoice
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
