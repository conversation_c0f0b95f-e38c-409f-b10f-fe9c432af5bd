@extends('layouts.app')

@section('title', 'Drafts')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row align-items-center justify-content-between mb-4">
        <div class="col-auto">
            <div class="mb-2">
                <h6 class="text-muted mb-1 fw-bold" style="font-size: 20px;">Invoice</h6>
                <h1 class="fw-bold mb-0" style="font-size: 40px; line-height: 1;">Drafts</h1>
            </div>
        </div>
        <div class="col-auto">
            <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create New
            </a>
        </div>
    </div>

    <!-- Drafts List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-light" style="border-radius: 15px; overflow: hidden;">
                <div class="card-body p-0">
                    @if($drafts->count() > 0)
                        @foreach($drafts as $index => $draft)
                            @if($index > 0)
                                <hr class="m-0" style="opacity: 0.5;">
                            @endif

                            <div class="draft-item d-flex align-items-center justify-content-between position-relative"
                                 style="background-color: #e9ecef; transition: background-color 0.2s ease; cursor: pointer; padding: 1rem;"
                                 onmouseover="this.style.backgroundColor='#f8f9fa'; this.querySelector('.draft-actions').style.opacity='1';"
                                 onmouseout="this.style.backgroundColor='#e9ecef'; this.querySelector('.draft-actions').style.opacity='0';"
                                 onclick="window.location.href='{{ route('invoices.edit', $draft->id) }}'">

                                <div class="flex-grow-1" style="flex: 1;">
                                    <div class="fw-medium" style="font-size: 0.875rem;">
                                        {{ $draft->title ?? $draft->recurring_settings['rebill_description'] ?? 'Created ' . $draft->created_at->format('M j, Y') }}
                                    </div>
                                    <small class="text-muted" style="font-size: 0.75rem;">#{{ $draft->id }}</small>
                                </div>

                                <div class="draft-actions d-flex align-items-center" style="opacity: 0; transition: opacity 0.2s ease; gap: 10px;">
                                    <form method="POST" action="{{ route('invoices.destroy', $draft->id) }}"
                                          class="mb-0"
                                          onsubmit="return confirm('Are you sure you want to delete this draft?')"
                                          onclick="event.stopPropagation();">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm" style="border: none; background: none; padding: 0.25rem;">
                                            <i class="fas fa-trash" style="color: #6c757d; font-size: 1.25rem;"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="d-flex align-items-center justify-content-center flex-column text-center py-5" style="gap: 10px; padding: 50px 0;">
                            <i class="fas fa-file-alt fa-2x text-muted"></i>
                            <p class="fw-medium text-muted mb-0">You don't have any drafts yet</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>


@endsection
