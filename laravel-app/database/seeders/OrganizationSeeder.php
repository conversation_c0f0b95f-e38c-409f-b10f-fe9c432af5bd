<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Organization;

class OrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Demo Organization for testing
        Organization::create([
            'bexio_org_id' => 'demo_org_123',
            'name' => 'Demo Company AG',
            'email' => '<EMAIL>',
            'address' => 'Musterstrasse 123',
            'zip' => '8001',
            'city' => 'Zürich',
            'contact_name' => 'Max Mustermann',
            'phone' => '+41 44 123 45 67',
            'country' => 'CH',
            'language' => 'de',
            'subscription_status' => 'trial',
            'subscription_model' => 'monthly',
            'subscription_start' => now()->subDays(5),
            'trial_ends_at' => now()->addDays(85), // 3 months trial
            'bexio_company_profile' => [
                'company_id' => 'demo_123',
                'company_name' => 'Demo Company AG',
                'address' => 'Musterstrasse 123',
                'postcode' => '8001',
                'city' => 'Zürich',
                'country_id' => 1,
                'legal_form' => 'AG'
            ]
        ]);

        // Active Organization
        Organization::create([
            'bexio_org_id' => 'active_org_456',
            'name' => 'Active Business GmbH',
            'email' => '<EMAIL>',
            'address' => 'Businesspark 456',
            'zip' => '3000',
            'city' => 'Bern',
            'contact_name' => 'Anna Müller',
            'phone' => '+41 31 987 65 43',
            'country' => 'CH',
            'language' => 'de',
            'subscription_status' => 'active',
            'subscription_model' => 'yearly',
            'subscription_start' => now()->subMonths(6),
            'activated_at' => now()->subMonths(6),
            'bexio_company_profile' => [
                'company_id' => 'active_456',
                'company_name' => 'Active Business GmbH',
                'address' => 'Businesspark 456',
                'postcode' => '3000',
                'city' => 'Bern',
                'country_id' => 1,
                'legal_form' => 'GmbH'
            ]
        ]);

        // Admin Organization (for admin users)
        Organization::create([
            'name' => 'Kim Rebill Admin',
            'email' => '<EMAIL>',
            'country' => 'CH',
            'language' => 'de',
            'subscription_status' => 'active',
            'subscription_model' => 'yearly',
            'subscription_start' => now()->subYear(),
            'activated_at' => now()->subYear(),
        ]);
    }
}
