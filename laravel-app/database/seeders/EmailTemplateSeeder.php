<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\EmailTemplate;

class EmailTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $templates = [
            [
                'organization_id' => null,
                'name' => 'Default Recurring Invoice Email',
                'type' => 'recurring_invoice',
                'subject' => 'Rechnung {invoice_number} - {company_name}',
                'body' => "Guten Tag {customer_name},

anbei erhalten Sie Ihre Rechnung {invoice_number} vom {invoice_date}.

Rechnungsdetails:
- Rechnungsnummer: {invoice_number}
- Rechnungsdatum: {invoice_date}
- Fälligkeitsdatum: {due_date}
- Rechnungsbetrag: CHF {total_amount}

Diese Rechnung wurde automatisch durch unser Recurring Invoice System erstellt.

[Network Link]

Bei Fragen stehen wir Ihnen gerne zur Verfügung.

Freundliche Grüsse
{company_name}

---
Diese E-Mail wurde automatisch generiert.
Kim Rebill - Recurring Invoice Management",
                'variables' => [
                    'customer_name' => 'Name des Kunden',
                    'invoice_number' => 'Rechnungsnummer',
                    'invoice_date' => 'Rechnungsdatum',
                    'due_date' => 'Fälligkeitsdatum',
                    'total_amount' => 'Rechnungsbetrag',
                    'company_name' => 'Firmenname'
                ],
                'is_default' => true,
                'is_active' => true
            ],
            [
                'organization_id' => null,
                'name' => 'Default Invoice Reminder',
                'type' => 'invoice_reminder',
                'subject' => 'Zahlungserinnerung - Rechnung {invoice_number}',
                'body' => "Guten Tag {customer_name},

wir möchten Sie daran erinnern, dass die Rechnung {invoice_number} vom {invoice_date} noch offen ist.

Rechnungsdetails:
- Rechnungsnummer: {invoice_number}
- Rechnungsdatum: {invoice_date}
- Fälligkeitsdatum: {due_date}
- Offener Betrag: CHF {total_amount}

Falls Sie die Zahlung bereits veranlasst haben, betrachten Sie diese E-Mail als gegenstandslos.

[Network Link]

Bei Fragen zur Rechnung stehen wir Ihnen gerne zur Verfügung.

Freundliche Grüsse
{company_name}",
                'variables' => [
                    'customer_name' => 'Name des Kunden',
                    'invoice_number' => 'Rechnungsnummer',
                    'invoice_date' => 'Rechnungsdatum',
                    'due_date' => 'Fälligkeitsdatum',
                    'total_amount' => 'Rechnungsbetrag',
                    'company_name' => 'Firmenname'
                ],
                'is_default' => true,
                'is_active' => true
            ],
            [
                'organization_id' => null,
                'name' => 'Default Welcome Email',
                'type' => 'welcome',
                'subject' => 'Willkommen bei {company_name}',
                'body' => "Guten Tag {customer_name},

herzlich willkommen bei {company_name}!

Wir freuen uns, Sie als neuen Kunden begrüssen zu dürfen. 

Ihre Kundendaten:
- Kundennummer: {customer_id}
- E-Mail: {customer_email}

Bei Fragen stehen wir Ihnen gerne zur Verfügung.

Freundliche Grüsse
{company_name}

---
Kim Rebill - Recurring Invoice Management",
                'variables' => [
                    'customer_name' => 'Name des Kunden',
                    'customer_id' => 'Kundennummer',
                    'customer_email' => 'Kunden E-Mail',
                    'company_name' => 'Firmenname'
                ],
                'is_default' => true,
                'is_active' => true
            ],
            [
                'organization_id' => null,
                'name' => 'Default Payment Confirmation',
                'type' => 'payment_confirmation',
                'subject' => 'Zahlungsbestätigung - Rechnung {invoice_number}',
                'body' => "Guten Tag {customer_name},

vielen Dank für Ihre Zahlung!

Wir bestätigen den Eingang Ihrer Zahlung für Rechnung {invoice_number}.

Zahlungsdetails:
- Rechnungsnummer: {invoice_number}
- Zahlungsbetrag: CHF {payment_amount}
- Zahlungsdatum: {payment_date}

Die Rechnung ist nun vollständig beglichen.

Freundliche Grüsse
{company_name}",
                'variables' => [
                    'customer_name' => 'Name des Kunden',
                    'invoice_number' => 'Rechnungsnummer',
                    'payment_amount' => 'Zahlungsbetrag',
                    'payment_date' => 'Zahlungsdatum',
                    'company_name' => 'Firmenname'
                ],
                'is_default' => true,
                'is_active' => true
            ]
        ];

        foreach ($templates as $templateData) {
            EmailTemplate::create($templateData);
        }

        echo "✅ Email templates created successfully!\n";
        echo "   - Recurring Invoice Email Template\n";
        echo "   - Invoice Reminder Template\n";
        echo "   - Welcome Email Template\n";
        echo "   - Payment Confirmation Template\n\n";
    }
}
