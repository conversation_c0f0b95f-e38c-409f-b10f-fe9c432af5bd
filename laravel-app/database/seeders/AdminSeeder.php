<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Organization;
use App\Models\Subscription;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create Admin Organization
        $adminOrg = Organization::create([
            'name' => 'Kim Rebill Admin',
            'email' => '<EMAIL>',
            'country' => 'CH',
            'language' => 'de',
            'subscription_status' => 'active',
            'subscription_model' => 'yearly',
            'subscription_start' => now()->subYear(),
            'activated_at' => now()->subYear(),
        ]);

        // Create Admin Subscription
        Subscription::create([
            'organization_id' => $adminOrg->id,
            'plan_type' => 'yearly',
            'price' => 0.00,
            'status' => 'active',
            'trial_ends_at' => null,
            'current_period_start' => now()->subYear(),
            'current_period_end' => now()->addYear(),
            'payment_method' => 'internal',
            'billing_details' => [
                'company_name' => 'Kim Rebill Admin',
                'internal_account' => true,
                'notes' => 'Internal admin organization'
            ],
            'notes' => 'Internal admin organization - no billing'
        ]);

        // Create Super Admin User
        User::create([
            'organization_id' => $adminOrg->id,
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin@2025'),
            'is_admin' => true,
            'is_active' => true,
            'role' => 'super_admin',
            'subscription_status' => 'active',
            'last_login_at' => now()->subDays(1),
        ]);

        echo "✅ Admin user created successfully!\n";
        echo "   Email: <EMAIL>\n";
        echo "   Password: admin@2025\n";
        echo "   Role: Super Admin\n\n";
    }
}
