<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Invoice;
use App\Models\User;
use App\Models\Organization;

class InvoiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $demoOrg = Organization::where('bexio_org_id', 'demo_org_123')->first();
        $activeOrg = Organization::where('bexio_org_id', 'active_org_456')->first();
        
        $demoUser = User::where('organization_id', $demoOrg->id)
                       ->where('email', '<EMAIL>')->first();
        $activeUser = User::where('organization_id', $activeOrg->id)
                         ->where('email', '<EMAIL>')->first();

        // Demo Organization Invoices
        $this->createInvoicesForOrganization($demoOrg, $demoUser);
        
        // Active Organization Invoices
        $this->createInvoicesForOrganization($activeOrg, $activeUser);
    }

    private function createInvoicesForOrganization($organization, $user)
    {
        $invoices = [
            // Recurring Invoice Template
            [
                'user_id' => $user->id,
                'organization_id' => $organization->id,
                'bexio_id' => null,
                'title' => 'Monthly Hosting Service',
                'document_nr' => 'REC-001',
                'contact_info' => [
                    'id' => 1001,
                    'name_1' => 'Tech Solutions AG',
                    'name_2' => '',
                    'address' => 'Techstrasse 10',
                    'postcode' => '8000',
                    'city' => 'Zürich',
                    'country_id' => 1,
                    'mail' => '<EMAIL>',
                    'phone' => '+41 44 555 0101'
                ],
                'total' => 299.00,
                'status' => 'draft',
                'is_recurring' => true,
                'recurring_settings' => [
                    'interval' => 'monthly',
                    'next_charge' => now()->addMonth()->format('Y-m-d'),
                    'start_date' => now()->format('Y-m-d'),
                    'end_date' => null
                ],
                'items' => [
                    [
                        'description' => 'Monthly Hosting Service',
                        'quantity' => 1,
                        'unit_price' => 299.00,
                        'total' => 299.00,
                        'tax_rate' => 7.7
                    ]
                ],
                'tax_status' => 0,
                'next_run_date' => now()->addMonth()->format('Y-m-d'),
                'created_from_recurring' => false
            ],

            // Another Recurring Template
            [
                'user_id' => $user->id,
                'organization_id' => $organization->id,
                'bexio_id' => null,
                'title' => 'Quarterly Maintenance',
                'document_nr' => 'REC-002',
                'contact_info' => [
                    'id' => 1002,
                    'name_1' => 'Manufacturing Corp',
                    'name_2' => '',
                    'address' => 'Industrieweg 25',
                    'postcode' => '4000',
                    'city' => 'Basel',
                    'country_id' => 1,
                    'mail' => '<EMAIL>',
                    'phone' => '+41 61 555 0202'
                ],
                'total' => 1200.00,
                'status' => 'draft',
                'is_recurring' => true,
                'recurring_settings' => [
                    'interval' => 'quarterly',
                    'next_charge' => now()->addMonths(3)->format('Y-m-d'),
                    'start_date' => now()->format('Y-m-d'),
                    'end_date' => null
                ],
                'items' => [
                    [
                        'description' => 'Quarterly System Maintenance',
                        'quantity' => 1,
                        'unit_price' => 1200.00,
                        'total' => 1200.00,
                        'tax_rate' => 7.7
                    ]
                ],
                'tax_status' => 0,
                'next_run_date' => now()->addMonths(3)->format('Y-m-d'),
                'created_from_recurring' => false
            ],

            // Generated from Recurring (sent invoice)
            [
                'user_id' => $user->id,
                'organization_id' => $organization->id,
                'bexio_id' => 'BX-2024-001',
                'title' => 'Monthly Hosting Service',
                'document_nr' => 'INV-2024-001',
                'contact_info' => [
                    'id' => 1001,
                    'name_1' => 'Tech Solutions AG',
                    'name_2' => '',
                    'address' => 'Techstrasse 10',
                    'postcode' => '8000',
                    'city' => 'Zürich',
                    'country_id' => 1,
                    'mail' => '<EMAIL>',
                    'phone' => '+41 44 555 0101'
                ],
                'total' => 299.00,
                'status' => 'sent',
                'is_recurring' => false,
                'recurring_settings' => null,
                'items' => [
                    [
                        'description' => 'Monthly Hosting Service - January 2024',
                        'quantity' => 1,
                        'unit_price' => 299.00,
                        'total' => 299.00,
                        'tax_rate' => 7.7
                    ]
                ],
                'tax_status' => 0,
                'bexio_created_at' => now()->subDays(15),
                'last_synced_at' => now()->subHours(2),
                'created_from_recurring' => true,
                'recurring_template_id' => 1
            ],

            // Regular Draft Invoice
            [
                'user_id' => $user->id,
                'organization_id' => $organization->id,
                'bexio_id' => null,
                'title' => 'Custom Development Project',
                'document_nr' => 'DRAFT-001',
                'contact_info' => [
                    'id' => 1003,
                    'name_1' => 'Startup Innovation GmbH',
                    'name_2' => '',
                    'address' => 'Innovation Hub 5',
                    'postcode' => '6000',
                    'city' => 'Luzern',
                    'country_id' => 1,
                    'mail' => '<EMAIL>',
                    'phone' => '+41 41 555 0303'
                ],
                'total' => 4500.00,
                'status' => 'draft',
                'is_recurring' => false,
                'recurring_settings' => null,
                'items' => [
                    [
                        'description' => 'Custom Web Application Development',
                        'quantity' => 45,
                        'unit_price' => 100.00,
                        'total' => 4500.00,
                        'tax_rate' => 7.7
                    ]
                ],
                'tax_status' => 0,
                'created_from_recurring' => false
            ],

            // Paid Invoice from Bexio
            [
                'user_id' => $user->id,
                'organization_id' => $organization->id,
                'bexio_id' => 'BX-2024-002',
                'title' => 'Consulting Services',
                'document_nr' => 'INV-2024-002',
                'contact_info' => [
                    'id' => 1004,
                    'name_1' => 'Consulting Partners AG',
                    'name_2' => '',
                    'address' => 'Business Center 12',
                    'postcode' => '9000',
                    'city' => 'St. Gallen',
                    'country_id' => 1,
                    'mail' => '<EMAIL>',
                    'phone' => '+41 71 555 0404'
                ],
                'total' => 2400.00,
                'status' => 'paid',
                'is_recurring' => false,
                'recurring_settings' => null,
                'items' => [
                    [
                        'description' => 'Business Process Consulting',
                        'quantity' => 24,
                        'unit_price' => 100.00,
                        'total' => 2400.00,
                        'tax_rate' => 7.7
                    ]
                ],
                'tax_status' => 0,
                'bexio_created_at' => now()->subDays(30),
                'last_synced_at' => now()->subHours(1),
                'created_from_recurring' => false
            ]
        ];

        foreach ($invoices as $invoiceData) {
            Invoice::create($invoiceData);
        }
    }
}
