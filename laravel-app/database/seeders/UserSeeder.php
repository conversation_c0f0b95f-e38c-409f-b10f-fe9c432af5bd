<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Organization;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $demoOrg = Organization::where('bexio_org_id', 'demo_org_123')->first();
        $activeOrg = Organization::where('bexio_org_id', 'active_org_456')->first();
        $adminOrg = Organization::where('name', '<PERSON> Rebill Admin')->first();

        // Demo Bexio User
        User::create([
            'organization_id' => $demoOrg->id,
            'bexio_id' => '12345',
            'name' => 'Demo User',
            'email' => '<EMAIL>',
            'access_token' => 'mock_access_token_demo',
            'refresh_token' => 'mock_refresh_token_demo',
            'token_expires_at' => now()->addHours(1),
            'refresh_token_rotated_at' => now(),
            'is_admin' => false,
            'is_active' => true,
            'role' => 'user',
            'subscription_status' => 'trial',
            'trial_ends_at' => now()->addDays(85),
            'last_login_at' => now()->subHours(2),
        ]);

        // Active Bexio User
        User::create([
            'organization_id' => $activeOrg->id,
            'bexio_id' => '67890',
            'name' => 'Active User',
            'email' => '<EMAIL>',
            'access_token' => 'mock_access_token_active',
            'refresh_token' => 'mock_refresh_token_active',
            'token_expires_at' => now()->addHours(1),
            'refresh_token_rotated_at' => now(),
            'is_admin' => false,
            'is_active' => true,
            'role' => 'user',
            'subscription_status' => 'active',
            'last_login_at' => now()->subMinutes(30),
        ]);

        // Organization Admin User
        User::create([
            'organization_id' => $demoOrg->id,
            'bexio_id' => '11111',
            'name' => 'Demo Admin',
            'email' => '<EMAIL>',
            'access_token' => 'mock_access_token_admin',
            'refresh_token' => 'mock_refresh_token_admin',
            'token_expires_at' => now()->addHours(1),
            'refresh_token_rotated_at' => now(),
            'is_admin' => true,
            'is_active' => true,
            'role' => 'admin',
            'subscription_status' => 'trial',
            'trial_ends_at' => now()->addDays(85),
            'last_login_at' => now()->subHours(1),
        ]);

        // Super Admin User (for system administration)
        User::create([
            'organization_id' => $adminOrg->id,
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin@2025'),
            'is_admin' => true,
            'is_active' => true,
            'role' => 'super_admin',
            'subscription_status' => 'active',
            'last_login_at' => now()->subDays(1),
        ]);

        // Additional test users for demo organization
        User::create([
            'organization_id' => $demoOrg->id,
            'bexio_id' => '22222',
            'name' => 'Test User 1',
            'email' => '<EMAIL>',
            'access_token' => 'mock_access_token_test1',
            'refresh_token' => 'mock_refresh_token_test1',
            'token_expires_at' => now()->addHours(1),
            'refresh_token_rotated_at' => now(),
            'is_admin' => false,
            'is_active' => true,
            'role' => 'user',
            'subscription_status' => 'trial',
            'trial_ends_at' => now()->addDays(85),
            'last_login_at' => now()->subDays(2),
        ]);

        User::create([
            'organization_id' => $activeOrg->id,
            'bexio_id' => '33333',
            'name' => 'Test User 2',
            'email' => '<EMAIL>',
            'access_token' => 'mock_access_token_test2',
            'refresh_token' => 'mock_refresh_token_test2',
            'token_expires_at' => now()->addHours(1),
            'refresh_token_rotated_at' => now(),
            'is_admin' => false,
            'is_active' => false, // Suspended user
            'role' => 'user',
            'subscription_status' => 'suspended',
            'last_login_at' => now()->subWeeks(2),
        ]);
    }
}
