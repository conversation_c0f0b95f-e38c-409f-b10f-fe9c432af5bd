<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\RecurringTemplate;
use App\Models\Invoice;
use App\Models\Organization;

class RecurringTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $demoOrg = Organization::where('bexio_org_id', 'demo_org_123')->first();
        $activeOrg = Organization::where('bexio_org_id', 'active_org_456')->first();

        // Get recurring invoices to create templates for
        $recurringInvoices = Invoice::where('is_recurring', true)
                                  ->where('created_from_recurring', false)
                                  ->get();

        foreach ($recurringInvoices as $invoice) {
            $intervalMapping = [
                'monthly' => 'monthly',
                'quarterly' => '3 months',
                'yearly' => 'yearly'
            ];

            $interval = $invoice->recurring_settings['interval'] ?? 'monthly';
            $intervalStr = $intervalMapping[$interval] ?? 'monthly';

            RecurringTemplate::create([
                'organization_id' => $invoice->organization_id,
                'contact_id' => $invoice->contact_info['id'],
                'invoice_id' => $invoice->id,
                'title' => $invoice->title,
                'positions' => $invoice->items,
                'start_date' => $invoice->recurring_settings['start_date'] ?? now()->format('Y-m-d'),
                'interval_str' => $intervalStr,
                'last_executed' => null,
                'interval' => $interval,
                'next_run' => $invoice->next_run_date ? $invoice->next_run_date . ' 09:00:00' : now()->addMonth()
            ]);
        }

        // Additional standalone recurring templates (not linked to invoices)
        $this->createStandaloneTemplates($demoOrg);
        $this->createStandaloneTemplates($activeOrg);
    }

    private function createStandaloneTemplates($organization)
    {
        $templates = [
            [
                'organization_id' => $organization->id,
                'contact_id' => 2001,
                'invoice_id' => null,
                'title' => 'Annual License Fee',
                'positions' => [
                    [
                        'description' => 'Software License - Annual',
                        'quantity' => 1,
                        'unit_price' => 1200.00,
                        'total' => 1200.00,
                        'tax_rate' => 7.7
                    ]
                ],
                'start_date' => now()->addMonths(2)->format('Y-m-d'),
                'interval_str' => 'yearly',
                'last_executed' => null,
                'interval' => 'yearly',
                'next_run' => now()->addMonths(2)->setTime(9, 0, 0)
            ],
            [
                'organization_id' => $organization->id,
                'contact_id' => 2002,
                'invoice_id' => null,
                'title' => 'Weekly Cleaning Service',
                'positions' => [
                    [
                        'description' => 'Office Cleaning Service',
                        'quantity' => 4,
                        'unit_price' => 75.00,
                        'total' => 300.00,
                        'tax_rate' => 7.7
                    ]
                ],
                'start_date' => now()->addWeek()->format('Y-m-d'),
                'interval_str' => 'weekly',
                'last_executed' => null,
                'interval' => 'weekly',
                'next_run' => now()->addWeek()->setTime(9, 0, 0)
            ],
            [
                'organization_id' => $organization->id,
                'contact_id' => 2003,
                'invoice_id' => null,
                'title' => 'Bi-Annual Security Audit',
                'positions' => [
                    [
                        'description' => 'IT Security Audit & Report',
                        'quantity' => 1,
                        'unit_price' => 2500.00,
                        'total' => 2500.00,
                        'tax_rate' => 7.7
                    ]
                ],
                'start_date' => now()->addMonths(6)->format('Y-m-d'),
                'interval_str' => '6 months',
                'last_executed' => null,
                'interval' => 'monthly', // Will be processed every 6 months
                'next_run' => now()->addMonths(6)->setTime(9, 0, 0)
            ]
        ];

        foreach ($templates as $templateData) {
            RecurringTemplate::create($templateData);
        }
    }
}
