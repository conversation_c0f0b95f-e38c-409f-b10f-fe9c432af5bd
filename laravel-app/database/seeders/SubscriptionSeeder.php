<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Subscription;
use App\Models\Organization;

class SubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $demoOrg = Organization::where('bexio_org_id', 'demo_org_123')->first();
        $activeOrg = Organization::where('bexio_org_id', 'active_org_456')->first();
        $adminOrg = Organization::where('name', 'Kim Rebill Admin')->first();

        // Demo Organization Subscription (Trial)
        Subscription::create([
            'organization_id' => $demoOrg->id,
            'plan_type' => 'monthly',
            'price' => 29.00,
            'status' => 'trial',
            'trial_ends_at' => now()->addDays(85),
            'current_period_start' => now()->subDays(5),
            'current_period_end' => now()->addDays(25),
            'payment_method' => null,
            'billing_details' => [
                'company_name' => 'Demo Company AG',
                'address' => 'Musterstrasse 123',
                'zip' => '8001',
                'city' => 'Zürich',
                'country' => 'CH',
                'vat_number' => 'CHE-123.456.789'
            ],
            'notes' => 'Trial subscription for demo organization'
        ]);

        // Active Organization Subscription (Yearly Active)
        Subscription::create([
            'organization_id' => $activeOrg->id,
            'plan_type' => 'yearly',
            'price' => 290.00,
            'status' => 'active',
            'trial_ends_at' => null,
            'current_period_start' => now()->subMonths(6),
            'current_period_end' => now()->addMonths(6),
            'payment_method' => 'credit_card',
            'billing_details' => [
                'company_name' => 'Active Business GmbH',
                'address' => 'Businesspark 456',
                'zip' => '3000',
                'city' => 'Bern',
                'country' => 'CH',
                'vat_number' => 'CHE-987.654.321',
                'payment_method' => 'Visa ending in 1234',
                'billing_email' => '<EMAIL>'
            ],
            'notes' => 'Yearly subscription - paid annually'
        ]);

        // Admin Organization Subscription (Active)
        Subscription::create([
            'organization_id' => $adminOrg->id,
            'plan_type' => 'yearly',
            'price' => 0.00,
            'status' => 'active',
            'trial_ends_at' => null,
            'current_period_start' => now()->subYear(),
            'current_period_end' => now()->addYear(),
            'payment_method' => 'internal',
            'billing_details' => [
                'company_name' => 'Kim Rebill Admin',
                'internal_account' => true,
                'notes' => 'Internal admin organization'
            ],
            'notes' => 'Internal admin organization - no billing'
        ]);
    }
}
