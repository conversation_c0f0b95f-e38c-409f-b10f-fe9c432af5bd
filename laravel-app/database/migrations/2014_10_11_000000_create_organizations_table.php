<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('organizations', function (Blueprint $table) {
            $table->id();
            $table->string('bexio_org_id')->unique()->nullable();
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('address')->nullable();
            $table->string('zip')->nullable();
            $table->string('city')->nullable();
            $table->string('contact_name')->nullable();
            $table->string('phone')->nullable();
            $table->text('refresh_token')->nullable();
            $table->string('country', 2)->default('CH');
            $table->string('language', 2)->default('de');
            $table->enum('subscription_status', ['trial', 'active', 'inactive'])->default('trial');
            $table->enum('subscription_model', ['monthly', 'yearly'])->nullable();
            $table->date('subscription_start')->nullable();
            $table->datetime('trial_ends_at')->nullable();
            $table->datetime('activated_at')->nullable();
            $table->json('bexio_company_profile')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('organizations');
    }
};
