<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->enum('plan_type', ['monthly', 'yearly'])->default('monthly');
            $table->decimal('price', 8, 2)->default(0.00);
            $table->enum('status', ['trial', 'active', 'cancelled', 'expired'])->default('trial');
            $table->datetime('trial_ends_at')->nullable();
            $table->datetime('current_period_start')->nullable();
            $table->datetime('current_period_end')->nullable();
            $table->datetime('cancelled_at')->nullable();
            $table->string('payment_method')->nullable();
            $table->json('billing_details')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->index(['organization_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscriptions');
    }
};
