<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->string('bexio_id')->nullable();
            $table->string('title');
            $table->string('document_nr')->nullable();
            $table->json('contact_info');
            $table->decimal('total', 10, 2);
            $table->enum('status', ['draft', 'sent', 'paid', 'cancelled', 'overdue'])->default('draft');
            $table->boolean('is_recurring')->default(false);
            $table->json('recurring_settings')->nullable();
            $table->json('items');
            $table->integer('tax_status')->default(0);
            $table->datetime('bexio_created_at')->nullable();
            $table->datetime('last_processed_at')->nullable();
            $table->date('next_run_date')->nullable();
            $table->unsignedBigInteger('recurring_template_id')->nullable();
            $table->boolean('created_from_recurring')->default(false);
            $table->datetime('last_synced_at')->nullable();
            $table->timestamps();
            
            $table->index(['organization_id', 'bexio_id']);
            $table->index(['organization_id', 'is_recurring']);
            $table->index(['organization_id', 'status']);
            $table->index(['user_id', 'is_recurring']);
            $table->index('next_run_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoices');
    }
};
