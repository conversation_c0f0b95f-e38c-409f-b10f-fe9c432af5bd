<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recurring_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->integer('contact_id');
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('title');
            $table->json('positions');
            $table->date('start_date');
            $table->string('interval_str', 20);
            $table->date('last_executed')->nullable();
            $table->enum('interval', ['daily', 'weekly', 'monthly', 'yearly'])->default('monthly');
            $table->datetime('next_run')->nullable();
            $table->timestamps();
            
            $table->index(['organization_id', 'contact_id']);
            $table->index(['organization_id', 'interval']);
            $table->index('next_run');
            $table->index('start_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recurring_templates');
    }
};
