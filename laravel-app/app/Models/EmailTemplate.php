<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'name',
        'type',
        'subject',
        'body',
        'variables',
        'is_default',
        'is_active',
    ];

    protected $casts = [
        'variables' => 'array',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    // Scopes
    public function scopeForOrganization($query, $organizationId)
    {
        return $query->where(function($q) use ($organizationId) {
            $q->where('organization_id', $organizationId)
              ->orWhere('is_default', true);
        });
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDefaults($query)
    {
        return $query->where('is_default', true);
    }

    // Helper Methods
    public function isDefault()
    {
        return $this->is_default;
    }

    public function isCustom()
    {
        return !$this->is_default && !is_null($this->organization_id);
    }

    public function renderTemplate($variables = [])
    {
        $subject = $this->subject;
        $body = $this->body;

        foreach ($variables as $key => $value) {
            $placeholder = '{' . $key . '}';
            $subject = str_replace($placeholder, $value, $subject);
            $body = str_replace($placeholder, $value, $body);
        }

        return [
            'subject' => $subject,
            'body' => $body
        ];
    }

    public function getAvailableVariables()
    {
        return $this->variables ?? [];
    }

    public static function getTemplateForOrganization($organizationId, $type)
    {
        // First try to get organization-specific template
        $template = static::where('organization_id', $organizationId)
                         ->where('type', $type)
                         ->where('is_active', true)
                         ->first();

        // If not found, get default template
        if (!$template) {
            $template = static::where('is_default', true)
                             ->where('type', $type)
                             ->where('is_active', true)
                             ->first();
        }

        return $template;
    }
}
