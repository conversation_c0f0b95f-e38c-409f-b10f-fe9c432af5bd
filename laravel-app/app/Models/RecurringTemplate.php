<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RecurringTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'contact_id',
        'invoice_id',
        'title',
        'positions',
        'start_date',
        'interval_str',
        'last_executed',
        'interval',
        'next_run',
    ];

    protected $casts = [
        'positions' => 'array',
        'start_date' => 'date',
        'last_executed' => 'date',
        'next_run' => 'datetime',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    // Scopes
    public function scopeForOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeActive($query)
    {
        return $query->whereNotNull('next_run');
    }

    public function scopeDueForExecution($query)
    {
        return $query->where('next_run', '<=', now());
    }

    public function scopeByInterval($query, $interval)
    {
        return $query->where('interval', $interval);
    }

    // Helper Methods
    public function isActive()
    {
        return !is_null($this->next_run);
    }

    public function isDue()
    {
        return $this->next_run && $this->next_run <= now();
    }

    public function getFormattedInterval()
    {
        $intervals = [
            'daily' => 'Daily',
            'weekly' => 'Weekly', 
            'monthly' => 'Monthly',
            'yearly' => 'Yearly'
        ];

        return $intervals[$this->interval] ?? ucfirst($this->interval);
    }

    public function getNextRunFormatted()
    {
        return $this->next_run ? $this->next_run->format('d.m.Y H:i') : 'Not scheduled';
    }

    public function getLastExecutedFormatted()
    {
        return $this->last_executed ? $this->last_executed->format('d.m.Y') : 'Never';
    }

    public function getTotalAmount()
    {
        if (!$this->positions || !is_array($this->positions)) {
            return 0;
        }

        return collect($this->positions)->sum('total');
    }

    public function markAsExecuted($executionDate = null)
    {
        $this->update([
            'last_executed' => $executionDate ?? now()->format('Y-m-d'),
            'next_run' => $this->calculateNextRun()
        ]);
    }

    public function calculateNextRun()
    {
        $baseDate = $this->last_executed ?? $this->start_date;
        
        if (!$baseDate) {
            return null;
        }

        $date = is_string($baseDate) ? \Carbon\Carbon::parse($baseDate) : $baseDate;

        switch ($this->interval) {
            case 'daily':
                return $date->addDay()->setTime(9, 0, 0);
            case 'weekly':
                return $date->addWeek()->setTime(9, 0, 0);
            case 'monthly':
                return $date->addMonth()->setTime(9, 0, 0);
            case 'yearly':
                return $date->addYear()->setTime(9, 0, 0);
            default:
                // Handle custom intervals like "3 months", "6 months"
                if (preg_match('/(\d+)\s*(month|week|day|year)s?/', $this->interval_str, $matches)) {
                    $amount = (int) $matches[1];
                    $unit = $matches[2];
                    
                    switch ($unit) {
                        case 'day':
                            return $date->addDays($amount)->setTime(9, 0, 0);
                        case 'week':
                            return $date->addWeeks($amount)->setTime(9, 0, 0);
                        case 'month':
                            return $date->addMonths($amount)->setTime(9, 0, 0);
                        case 'year':
                            return $date->addYears($amount)->setTime(9, 0, 0);
                    }
                }
                return $date->addMonth()->setTime(9, 0, 0);
        }
    }

    public function pause()
    {
        $this->update(['next_run' => null]);
    }

    public function resume()
    {
        $this->update(['next_run' => $this->calculateNextRun()]);
    }

    public function getContactInfo()
    {
        // This would typically fetch from Bexio API or local contacts table
        // For now, return basic structure
        return [
            'id' => $this->contact_id,
            'name' => 'Contact ' . $this->contact_id,
            'email' => 'contact' . $this->contact_id . '@example.com'
        ];
    }
}
