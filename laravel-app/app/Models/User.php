<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'bexio_id',
        'name',
        'email',
        'organization_id',
        'is_admin',
        'is_active',
        'trial_ends_at',
        'subscription_status',
        'last_login_at',
        'access_token',
        'refresh_token',
        'token_expires_at',
        'refresh_token_rotated_at',
        'password',
        'template_language',
        'email_subject',
        'email_body',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'token_expires_at' => 'datetime',
        'refresh_token_rotated_at' => 'datetime',
        'last_login_at' => 'datetime',
        'trial_ends_at' => 'date',
        'is_admin' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function recurringTemplates()
    {
        return $this->hasMany(RecurringTemplate::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    // Helper Methods
    public function isAdmin()
    {
        return $this->is_admin;
    }

    public function isSuperAdmin()
    {
        return $this->role === 'super_admin';
    }

    public function canManageOrganization()
    {
        return $this->isAdmin() || $this->isSuperAdmin();
    }

    public function hasActiveSubscription()
    {
        return $this->organization &&
               $this->organization->subscription &&
               $this->organization->subscription->isActive();
    }

    public function canCreateInvoices()
    {
        return $this->is_active &&
               $this->organization &&
               $this->organization->canCreateInvoices();
    }

    public function updateLastLogin()
    {
        $this->update(['last_login_at' => now()]);
    }

    // Admin-related helper methods (updated to use is_admin field)

    public function isTrialUser()
    {
        return $this->subscription_status === 'trial';
    }

    public function isTrialExpired()
    {
        return $this->trial_ends_at && $this->trial_ends_at < now()->toDateString();
    }

    public function getRemainingTrialDays()
    {
        if (!$this->trial_ends_at) {
            return null;
        }

        $remaining = now()->diffInDays($this->trial_ends_at, false);
        return max(0, $remaining);
    }

    public function extendTrial($days)
    {
        $currentEndDate = $this->trial_ends_at ?? now()->toDateString();
        $this->update([
            'trial_ends_at' => now()->parse($currentEndDate)->addDays($days)->toDateString(),
            'subscription_status' => 'trial'
        ]);
    }

    public function activateSubscription()
    {
        $this->update([
            'subscription_status' => 'active',
            'trial_ends_at' => null
        ]);
    }

    public function suspendUser()
    {
        $this->update([
            'is_active' => false,
            'subscription_status' => 'suspended'
        ]);
    }

    public function reactivateUser()
    {
        $this->update([
            'is_active' => true,
            'subscription_status' => $this->trial_ends_at ? 'trial' : 'active'
        ]);
    }

    // Scopes for admin queries
    public function scopeAdmins($query)
    {
        return $query->where('is_admin', true);
    }

    public function scopeActiveUsers($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeTrialUsers($query)
    {
        return $query->where('subscription_status', 'trial');
    }

    public function scopeExpiredTrials($query)
    {
        return $query->where('subscription_status', 'trial')
                    ->where('trial_ends_at', '<', now()->toDateString());
    }
}
