<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Invoice;
use App\Models\User;
use Carbon\Carbon;

echo "🧪 TESTING INSERT INVOICE WITH RECURRING TODAY" . PHP_EOL;
echo str_repeat('=', 60) . PHP_EOL;

try {
    // Get user for testing (first user with access token)
    $user = User::whereNotNull('access_token')->first();

    if (!$user) {
        echo "❌ No user with access token found. Please login first." . PHP_EOL;
        exit(1);
    }

    echo "👤 Using user: {$user->name} ({$user->email})" . PHP_EOL;
    echo "🏢 Organization: {$user->organization_id}" . PHP_EOL;

    // Test invoice data
    $invoiceData = [
        'user_id' => $user->id,
        'organization_id' => $user->organization_id,
        'bexio_id' => 'TEST-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
        'title' => 'Test Recurring Invoice - ' . date('Y-m-d H:i:s'),
        'document_nr' => 'INV-TEST-' . date('Ymd-His'),
        'contact_info' => [
            'id' => 999,
            'contact_id' => 999,
            'name' => 'Doni Bageur',
            'email' => '<EMAIL>',
            'address' => 'Test Address 123, Jakarta'
        ],
        'total' => 150.00,
        'status' => 'active',
        'is_recurring' => true,
        'billing_period' => 'monthly',
        'billing_period' => 'monthly',
        'start_date' => Carbon::today()->toDateString(), // Today
        'next_run_date' => Carbon::today()->toDateString(), // Today for immediate processing
        'items' => [
            [
                'name' => 'Test Service Item',
                'description' => 'Monthly subscription service',
                'quantity' => 1,
                'price' => 150.00,
                'unit_id' => 1,
                'tax_id' => 1,
                'total' => 150.00
            ]
        ],
        'tax_status' => 1, // 1 = inclusive, 0 = exclusive
        'created_at' => now(),
        'updated_at' => now()
    ];

    echo PHP_EOL . "📋 Invoice Data:" . PHP_EOL;
    echo "- Title: {$invoiceData['title']}" . PHP_EOL;
    echo "- Document Nr: {$invoiceData['document_nr']}" . PHP_EOL;
    echo "- Customer: {$invoiceData['contact_info']['name']}" . PHP_EOL;
    echo "- Email: {$invoiceData['contact_info']['email']}" . PHP_EOL;
    echo "- Total: \${$invoiceData['total']}" . PHP_EOL;
    echo "- Billing Period: {$invoiceData['billing_period']}" . PHP_EOL;
    echo "- Start Date: {$invoiceData['start_date']}" . PHP_EOL;
    echo "- Next Billing: {$invoiceData['next_billing_date']}" . PHP_EOL;

    // Insert invoice
    echo PHP_EOL . "💾 Inserting invoice..." . PHP_EOL;

    $invoice = Invoice::create($invoiceData);

    echo "✅ Invoice created successfully!" . PHP_EOL;
    echo "- ID: {$invoice->id}" . PHP_EOL;
    echo "- Bexio ID: {$invoice->bexio_id}" . PHP_EOL;
    echo "- Status: {$invoice->status}" . PHP_EOL;
    echo "- Is Recurring: " . ($invoice->is_recurring ? 'Yes' : 'No') . PHP_EOL;

    // Verify invoice in database
    echo PHP_EOL . "🔍 Verifying invoice in database..." . PHP_EOL;

    $savedInvoice = Invoice::find($invoice->id);
    if ($savedInvoice) {
        echo "✅ Invoice verified in database" . PHP_EOL;
        echo "- Contact Info: " . json_encode($savedInvoice->contact_info) . PHP_EOL;
        echo "- Items: " . json_encode($savedInvoice->items) . PHP_EOL;
    } else {
        echo "❌ Invoice not found in database" . PHP_EOL;
    }

    // Check if invoice should be processed today
    echo PHP_EOL . "📅 Checking recurring schedule..." . PHP_EOL;

    $today = Carbon::today();
    $nextBilling = Carbon::parse($savedInvoice->next_billing_date);

    if ($nextBilling->lte($today)) {
        echo "✅ Invoice is due for processing TODAY!" . PHP_EOL;
        echo "- Today: {$today->toDateString()}" . PHP_EOL;
        echo "- Next Billing: {$nextBilling->toDateString()}" . PHP_EOL;
    } else {
        echo "⏰ Invoice scheduled for future processing" . PHP_EOL;
        echo "- Today: {$today->toDateString()}" . PHP_EOL;
        echo "- Next Billing: {$nextBilling->toDateString()}" . PHP_EOL;
    }

    // Show summary
    echo PHP_EOL . "📊 SUMMARY:" . PHP_EOL;
    echo "✅ Test invoice created with recurring date TODAY" . PHP_EOL;
    echo "✅ Customer email: <EMAIL>" . PHP_EOL;
    echo "✅ Ready for recurring invoice processing" . PHP_EOL;
    echo "✅ Invoice ID: {$invoice->id} for reference" . PHP_EOL;

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . PHP_EOL;
    exit(1);
}

echo PHP_EOL . "🎉 Test completed successfully!" . PHP_EOL;
